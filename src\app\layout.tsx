import type { Metada<PERSON> } from "next";
import { Inter } from "next/font/google";
import "./globals.css";
import NextAuthSessionProvider from "@/components/providers/session-provider";

const inter = Inter({
  variable: "--font-inter",
  subsets: ["latin"],
});

export const metadata: Metadata = {
  title: "Braling Invite",
  description: "Buat Undangan Pernikahan Online",
};

export default function RootLayout({
  children,
}: Readonly<{
  children: React.ReactNode;
}>) {
  return (
    <html lang="en">
      <body className={`${inter.variable} antialiased`}>
        <NextAuthSessionProvider>
          <main>{children}</main>
        </NextAuthSessionProvider>
      </body>
    </html>
  );
}
